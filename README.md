# Entry point
`python main.py` or `python test.py`

# Module test
The corresponding test file is in `tests` folder.
Run with `python -m test.test_xxx`, the log file is in `logs` folder.

# Config file
The default config file is in `config/default.yaml`.
You can specify a different config file with `--config path/to/config.yaml`.

# Prompts
The prompts are in `config/prompts.yaml`.
You can specify a different prompt file with `--prompt path/to/prompts.yaml`.

# Core
The core modules are in `core` folder.

# Tools
The tools are in `tools` folder.

# Utils
The utils are in `utils` folder.

# Logging
The log file is in `logs` folder.

# Dataset
The dataset is in `dataset` folder.

# TODO
See `Todo` file.