from ollama import chat
from typing import Optional
from core.tool_functions import LLM_Toolbox
import ollama, json, logging
logger = logging.getLogger(__name__)


class Chatbot:
    """
    Interface to Ollama LLM with tool calling capabilities
    
    Args:
            model: Name of the Ollama model to use
            system_prompt: System prompt to set context
            options: Model options like temperature, top_p, etc.
            toolbox: Optional LLM_Toolbox instance with registered tools
    """
    def __init__(self,
                 model_params: dict, 
                 prompt_pool: dict,
                #  model: str, 
                #  system_prompt_pool: dict, 
                #  options: dict,
                 toolbox: Optional[LLM_Toolbox] = None):
        self.model = model_params['conversation_model']
        self.embedding_model = model_params['embedding_model'] # currently not used
        self.options = model_params['options']
        self.think = model_params['think'] # default think mode
        
        self.system_prompt_pool = prompt_pool['sys_prompts']
        self.user_prompt_pool = prompt_pool['user_prompts'] # currently not used
        
        self.current_system_prompt_key = 'default'
        self.current_system_prompt =  self.system_prompt_pool[self.current_system_prompt_key]

        self.toolbox = toolbox
       
        self.reset_messages()
        logger.info(f"Initialized chatbot with model: {self.model}")
    
    ################# Conversation Basics #################    
    def single_turn(self, user_input=''):
        '''
        Run a single turn of the chatbot.
        
        Args:
            user_input: User input to the chatbot. If not provided, user input is taken from the command line.
        '''
        logger.info(f"Running single turn...")
        
        # Get user input
        if not user_input:
            user_input = input('>>>>>> Write a prompt for the chatbot: ')
            
        # Add user input to the messages
        self.messages.append(
            {
                'role': 'user',
                'content': user_input,
            }
        )
        
        # Prepare tools if available
        tools = self.toolbox.get_schemas() if self.toolbox else None

        # Call the model
        if self.think:
            response = chat(
                model=self.model,
                messages=self.messages,
                tools=tools,
                options=self.options,
            )
        else:
            response = chat(
                model=self.model,
                messages=self.messages,
                think=False,
                tools=tools,
                options=self.options,
            )
        
        # Process tool calls if present
        if response.message.tool_calls and self.toolbox:
            # Add the function calling response to messages
            self.messages.append(response.message)
            
            # Handle tool calls
            for tool_call in response.message.tool_calls:
                function_name = tool_call.function.name
                function_args = tool_call.function.arguments
                logger.info(f"Tool call: {function_name} with args: {function_args}")
                
                # Dispatch to toolbox to validate and execute
                result = self.toolbox.safe_dispatch({
                    "name": function_name,
                    "arguments": function_args
                })
                logger.info(f"Tool result: {result}")
                
                # Add tool result to messages
                self.messages.append(
                    {
                        'role': 'tool',
                        'content': json.dumps(result),
                        'name': function_name,
                    }
                )
                
                # Get final response with tool results without tool calls
                if self.think:
                    response = chat(
                        model=self.model,
                        messages=self.messages,
                        options=self.options,
                    )
                else:
                    response = chat(
                        model=self.model,
                        messages=self.messages,
                        options=self.options,
                        think=False,  
                    )
                
                # Add final response to history
                self.messages.append(
                    {
                        'role': response.message.role,
                        'content': response.message.content,
                    }
                )
        
        # No tool calls: update messages
        else:
            self.messages.append(
                {
                    'role': response.message.role,
                    'content': response.message.content,
                }
            )


    def single_turn_streaming(self, user_input=''):
        '''
        Run a single turn of the chatbot and flush the output to the console.
        
        Args:
            user_input: User input to the chatbot. If not provided, user input is taken from the command line.
        '''
        logger.info(f"Running single turn with streaming output...")
        
        # Get user input
        if not user_input:
            user_input = input('>>>>>> Write a prompt for the chatbot: ')
            
        # Add user input to the messages
        self.messages.append(
            {
                'role': 'user',
                'content': user_input,
            }
        )
        
        # Prepare tools if available
        tools = self.toolbox.get_schemas() if self.toolbox else None
        
        # Call the model
        if self.think:
            response = chat(
                model=self.model,
                messages=self.messages,
                tools=tools,
                options=self.options,
                stream=True,    
            )
        else:
            response = chat(
                model=self.model,
                messages=self.messages,
                think=False,
                tools=tools,
                options=self.options,
                stream=True,    
            )
        
        # Stream the response
        response_str = ''
        tool_calls_chunk = None
        final_combined_chunk = None
        for chunk in response:
            part = chunk.message.content
            print(part, end='', flush=True)
            response_str += part # accumulate the response
            
            if chunk.message.get('tool_calls') is not None:
                tool_calls_chunk = chunk
            final_combined_chunk = chunk
        print('\n')
        
        # Combine tool calls and final response
        final_combined_chunk.message.content = response_str
        final_combined_chunk.message.tool_calls = tool_calls_chunk.message.tool_calls if tool_calls_chunk else None
        logger.debug(f"Final combined chunk: {final_combined_chunk.message}")

        # Process tool calls if present
        if tool_calls_chunk and final_combined_chunk.message.tool_calls and self.toolbox:
            # Add the function calling response to messages
            self.messages.append(final_combined_chunk.message)
            
            # Handle tool calls
            for tool_call in tool_calls_chunk.message.tool_calls:
                function_name = tool_call.function.name
                function_args = tool_call.function.arguments
                logger.info(f"Tool call: {function_name} with args: {function_args}")
                
                # Dispatch to toolbox to validate and execute
                result = self.toolbox.safe_dispatch({
                    "name": function_name,
                    "arguments": function_args
                })
                logger.info(f"Tool result: {result}")
                
                # Add tool result to messages
                self.messages.append(
                    {
                        'role': 'tool',
                        'content': json.dumps(result),
                        'name': function_name,
                    }
                )
                
                # Get final response with tool results without tool calls
                if self.think:
                    response = chat(
                        model=self.model,
                        messages=self.messages,
                        options=self.options,
                        stream=True,    
                    )
                else:
                    response = chat(
                        model=self.model,
                        messages=self.messages,
                        think=False,
                        options=self.options,
                        stream=True,    
                    )
                
                response_str = ''
                for chunk in response:
                    part = chunk['message']['content']
                    print(part, end='', flush=True)
                    response_str += part # accumulate the response
                print('\n')
                
                # Add final response to history
                self.messages.append(
                    {
                        'role': 'assistant',
                        'content': response_str,
                    }
                )

        # No tool calls: update messages
        else:
            self.messages.append(
                {
                    'role': 'assistant',
                    'content': response_str,
                }
            )
    
    
    def convertation_with_history(self):
        """
        Start an interactive conversation with the chatbot, maintaining history between turns.
        
        This method provides a command-line interface for interacting with the chatbot.
        It supports various commands for controlling the conversation flow, managing system
        prompts, and viewing conversation history.
        
        Commands:
            /exit: Exit the conversation
            /help: Show available commands
            /think_mode: Show current thinking mode status
            /switch_think: Toggle thinking mode
            /set_system_prompt: Change the system prompt
            /print_system_prompt: Display current system prompt
            /print_available_system_prompts: List all available system prompts
            /print_questions: Show history of user questions
            /print_last_answer: Show the most recent assistant response
            /print_answers: Show history of all assistant responses
            /print_conversation: Show complete conversation history
            /clear_history: Reset conversation history
        
        Returns:
            None
        """
        print('Start conversation with history...')
        print('Type "/exit" to exit the conversation.')
        print('Type "/help" to see the available commands.')
        
        while True:
            user_input = input('>>>>>> Write a prompt for the chatbot: ')
            
            if user_input == "/exit":
                print('Exiting conversation...')
                break
            elif user_input == "/help":
                self.convertation_help()
            
            elif user_input == "/think_mode":
                self.print_think_mode()
            elif user_input == "/switch_think":
                self.switch_think()
                
            elif user_input == "/set_system_prompt":
                print('Setting system prompt...')
                print('>>>>>> Current system prompt:')
                self.print_system_prompt(key_only=True)
                
                print('>>>>>> Available system prompts:')
                self.print_available_system_prompts(key_only=True)
                
                prompt_key = input('Write the key of the system prompt: ')
                clear_history = input('Clear conversation history? (y/n): ') == 'y'
                self.set_system_prompt(prompt_key, clear_history)
            elif user_input == "/print_system_prompt":
                self.print_system_prompt()
            elif user_input == "/print_available_system_prompts":
                self.print_available_system_prompts()
                
            elif user_input == "/print_questions":
                self.print_questions_history()
            elif user_input == "/print_last_answer":
                self.print_last_answer()
            elif user_input == "/print_answers":
                self.print_answers_history()
            elif user_input == "/print_conversation":
                self.print_conversation_history()
            
            elif user_input == "/clear_history":
                self.reset_messages()
            
            # not valid command
            elif user_input.startswith('/'):
                print('Command not found. Type "/help" to see the available commands.')
            
            ################# response to user input #################
            else:
                self.single_turn_streaming(user_input)


    ################# Chatbot Functions #################    
    def convertation_help(self):
        print('-------------------- Help --------------------')
        
        print('"/exit": exit the conversation')
        print('"/help": show the available commands')
        
        print('"/think_mode": print the current think mode')
        print('"/switch_think": switch between think mode and no think mode"')
        
        print('"/set_system_prompt": set the system prompt')
        print('"/print_available_system_prompts": print the available system prompts')
        print('"/print_system_prompt": print the system prompt')
    
        print('"/print_questions": print the questions history')
        print('"/print_last_answer": print the last answer')
        print('"/print_answers": print the answers history')
        print('"/print_conversation": print the conversation history')
        
        print('"/clear_history": clear the conversation history')
        
        print('-------------------- End of help --------------------')


    def reset_messages(self):
        self.messages = [
            {
                'role': 'system',
                'content': self.current_system_prompt,
            },
        ]
        logger.info("Conversation history reset")


    def print_think_mode(self):
        print(f'Is current thinking mode activated: {self.think}')


    def switch_think(self):
        self.think = not self.think
        print(f'Switched think mode to: {self.think}')


    def set_system_prompt(self, prompt_key: str, reset_messages: bool = True):
        if prompt_key in self.system_prompt_pool:
            self.current_system_prompt = self.system_prompt_pool[prompt_key]
            if reset_messages:
                self.reset_messages()
            else:
                if self.messages[0]['role'] == 'system':
                    self.messages[0]['content'] = self.current_system_prompt
                else:
                    print('Cannot set system prompt. The first message is not a system message.')
        else:
            print('System prompt not found. Try again.')


    def print_last_answer(self):
        if self.messages[-1]['role'] == 'assistant':
            message = self.messages[-1]
            print(message['content'])
            if isinstance(message, ollama._types.Message):
                    for tool_call in message.tool_calls:
                        print('\t >>>>> ' + tool_call.function.name + ' >>>>>>')
                        print('\t', tool_call.function.arguments)
        
        else:
            print('No answer to print. The last message is not from the assistant.')


    def print_available_system_prompts(self, key_only: bool = False):
        if key_only:
            print(", ".join(str(k) for k in self.system_prompt_pool.keys()))
        else:
            print('-------------------- Available system prompts --------------------')
            for prompt_key in self.system_prompt_pool.keys():
                print('>>>>>> ' + prompt_key + ' >>>>>>')
                print(self.system_prompt_pool[prompt_key]) 
            print('-------------------- End of available system prompts --------------------')


    def print_system_prompt(self, key_only: bool = False):
        if key_only:
            print(self.current_system_prompt_key)
        else:
            print('-------------------- Start of system prompt --------------------')
            print(self.current_system_prompt_key + ':')
            print(self.current_system_prompt)
            print('-------------------- End of system prompt --------------------')


    def print_questions_history(self):
        print('-------------------- Start of question history --------------------')
        
        for message in self.messages:
            if message['role'] == 'user':
                print('>>>>>> ' + message['role'] + ' >>>>>>')
                print(message['content'])
        
        print('-------------------- End of question history --------------------')


    def print_answers_history(self):
        print('-------------------- Start of answer history --------------------')
        
        for message in self.messages:
            if message['role'] == 'assistant':
                print('>>>>>> ' + message['role'] + ' >>>>>>')
                print(message['content'])
                if isinstance(message, ollama._types.Message):
                    for tool_call in message.tool_calls:
                        print('\t >>>>> ' + tool_call.function.name + ' >>>>>>')
                        print('\t', tool_call.function.arguments)
        
        print('-------------------- End of answer history --------------------')


    def print_conversation_history(self):
        print('-------------------- Start of conversation history --------------------')
        
        for message in self.messages:
            print('>>>>>> ' + message['role'] + ' >>>>>>')
            print(message['content'])
            if isinstance(message, ollama._types.Message):
                for tool_call in message.tool_calls:
                    print('\t >>>>> ' + tool_call.function.name + ' >>>>>>')
                    print('\t', tool_call.function.arguments)
                
        
        print('-------------------- End of conversation history --------------------')
