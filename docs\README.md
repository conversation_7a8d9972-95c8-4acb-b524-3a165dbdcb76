# Documentation

This directory contains documentation for the project.

## Contents

- [Project Overview](project_overview.md): High-level overview of the project
- [Tools Framework](tools_readme.md): Documentation for the LLM tools framework
- [Transcript Processing](transcript_processing.md): Documentation for transcript processing
- [Configuration](configuration.md): Documentation for the configuration system
- [Testing](testing.md): Documentation for the testing framework
- [Development Guide](development_guide.md): Guidelines for developers

## Additional Resources

- [Main README](../README.md): Main project README
- [Config Files](../config/): Configuration files
- [Test Files](../tests/): Test files