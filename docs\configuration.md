# Configuration System

This document describes the configuration system used in the project.

## Configuration Files

The project uses YAML files for configuration:

- **config/default.yaml**: Default configuration values
- **config/prompts.yaml**: System and user prompts

## Configuration Structure

### LLM Parameters

```yaml
llm_params:
  conversation_model: "qwen3:0.6b"
  embedding_model: "qwen3:0.6b"
  options:
    temperature: 0.3
    seed: 42
    top_p: 0.9
  think: true
```

### Dataset Parameters

```yaml
dataset_params:
  path: "path/to/dataset"
```

### Prompt Pools

```yaml
prompt_pools:
  sys_prompts:
    default: ""
    function_calling: "You are an assistant that may call tools..."
    question_answering: "You are an assistant that answers questions..."
  user_prompts:
    extract_metadata: "Extract the metadata from the following text..."
    extract_qa_pairs: "Extract the question and answer pairs..."
```

## Command Line Overrides

Configuration values can be overridden via command line arguments:

```
python main.py --config custom_config.yaml --temperature 0.5 --seed 123
```

## Configuration Class

The `Config` class in `utils/config.py` handles:

- Loading configuration from YAML files
- Parsing command line arguments
- Providing access to configuration values

## Usage Examples

```python
# Load configuration
config = parse_args()

# Access configuration values
model_name = config.conversation_model
temperature = config.temperature

# Access options dictionary
options = config.options
```

## Best Practices

1. **Default Values**: Provide sensible defaults for all configuration values
2. **Documentation**: Document all configuration options
3. **Validation**: Validate configuration values before use
4. **Separation**: Separate configuration from code
5. **Environment Variables**: Support environment variables for sensitive values