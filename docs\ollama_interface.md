# Ollama Interface

This document describes the Ollama interface used in the project for interacting with LLMs.

## Overview

The `Chatbot` class in `core/ollama_interface.py` provides an interface to the Ollama API for LLM interactions. It supports:

- Single-turn conversations
- Streaming responses
- Interactive conversations with history
- Tool calling capabilities
- System prompt management
- Conversation history management

## Key Components

### Initialization

The chatbot is initialized with model parameters, prompt pools, and an optional toolbox:

```python
chatbot = Chatbot(
    model_params={
        "conversation_model": "qwen3:0.6b",
        "embedding_model": "qwen3:0.6b",
        "options": {
            "temperature": 0.3,
            "seed": 42,
            "top_p": 0.9
        },
        "think": True
    },
    prompt_pool={
        "sys_prompts": {
            "default": "You are a helpful assistant.",
            "function_calling": "You are an assistant that can call tools..."
        },
        "user_prompts": {}
    },
    toolbox=toolbox  # Optional LLM_Toolbox instance
)
```

### Conversation Methods

#### Single Turn

`single_turn(user_input='')`: Process a single user input and generate a response.

- Handles tool calls if present
- Updates conversation history
- Returns the assistant's response

#### Single Turn Streaming

`single_turn_streaming(user_input='')`: Similar to `single_turn`, but streams the response to the console.

- Shows the response as it's being generated
- Handles tool calls if present
- Updates conversation history

#### Conversation with History

`convertation_with_history()`: Start an interactive conversation with the chatbot.

- Maintains conversation history between turns
- Provides commands for controlling the conversation
- Supports system prompt management
- Allows viewing conversation history

### Command Interface

The `convertation_with_history()` method supports the following commands:

| Command | Description |
|---------|-------------|
| `/exit` | Exit the conversation |
| `/help` | Show available commands |
| `/think_mode` | Show current thinking mode status |
| `/switch_think` | Toggle thinking mode |
| `/set_system_prompt` | Change the system prompt |
| `/print_system_prompt` | Display current system prompt |
| `/print_available_system_prompts` | List all available system prompts |
| `/print_questions` | Show history of user questions |
| `/print_last_answer` | Show the most recent assistant response |
| `/print_answers` | Show history of all assistant responses |
| `/print_conversation` | Show complete conversation history |
| `/clear_history` | Reset conversation history |

### System Prompt Management

The chatbot supports multiple system prompts stored in a prompt pool:

- `set_system_prompt(prompt_key, reset_messages)`: Set the system prompt by key
- `print_system_prompt()`: Display the current system prompt
- `print_available_system_prompts()`: List all available system prompts

### Conversation History Management

The chatbot maintains conversation history and provides methods for viewing it:

- `reset_messages()`: Clear conversation history
- `print_questions_history()`: Show history of user questions
- `print_answers_history()`: Show history of assistant responses
- `print_conversation_history()`: Show complete conversation history
- `print_last_answer()`: Show the most recent assistant response

### Think Mode

The chatbot supports a "think mode" that controls whether the LLM shows its reasoning:

- `print_think_mode()`: Show current thinking mode status
- `switch_think()`: Toggle thinking mode

## Usage Examples

### Basic Conversation

```python
from core.ollama_interface import Chatbot
from utils.config import parse_args

# Load configuration
config = parse_args()

# Create chatbot
chatbot = Chatbot(
    model_params=config.llm_params,
    prompt_pool=config.prompt_pools
)

# Start conversation
chatbot.convertation_with_history()
```

### Single Turn with Tool Calling

```python
from core.ollama_interface import Chatbot
from core.tool_functions import LLM_Toolbox
from tools.visualization_tools import draw_sine_wave_tool

# Set up toolbox
toolbox = LLM_Toolbox()
toolbox.register(draw_sine_wave_tool)

# Create chatbot with tools
chatbot = Chatbot(
    model_params={"conversation_model": "qwen3:0.6b", "think": True},
    prompt_pool={"sys_prompts": {"default": "You are a helpful assistant."}},
    toolbox=toolbox
)

# Run a single turn
chatbot.single_turn("Draw a sine wave with frequency 3 and duration 7")
```

## Best Practices

1. **System Prompts**: Use specific system prompts for different tasks
2. **Tool Registration**: Register all tools before starting the conversation
3. **Error Handling**: Handle tool execution errors gracefully
4. **Streaming**: Use streaming for better user experience with long responses
5. **Think Mode**: Enable think mode for debugging and transparency