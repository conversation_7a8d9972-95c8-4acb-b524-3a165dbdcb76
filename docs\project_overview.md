# Project Overview

This document provides a high-level overview of the project architecture, components, and workflow.

## Project Architecture

```
project/
├── config/           # Configuration files
├── core/             # Core functionality
├── docs/             # Documentation
├── tools/            # Tool implementations
├── utils/            # Utility functions
├── tests/            # Test scripts
├── logs/             # Log files
├── main.py           # Main application entry point
└── README.md         # Main project README
```

## Key Components

### Core Components

- **ollama_interface.py**: Interface to the Ollama LLM API
- **tool_functions.py**: Framework for LLM function calling
- **transcript_parsing.py**: Transcript processing and analysis

### Tools

- **visualization_tools.py**: Data visualization tools
- **transcript_tools.py**: Transcript analysis tools

### Utilities

- **config.py**: Configuration management
- **logger_setup.py**: Logging setup

## Workflow

1. **Configuration**: Load configuration from YAML files
2. **Tool Registration**: Register tools with the LLM_Toolbox
3. **LLM Initialization**: Initialize the LLM with the Ollama API
4. **User Interaction**: Process user input and generate responses
5. **Tool Execution**: Execute tools based on LLM function calls
6. **Response Generation**: Generate responses based on tool results

## Data Flow

```
User Input → LLM → Tool Selection → Tool Execution → Result Processing → Response Generation → User Output
```

## Extension Points

- **New Tools**: Add new tools in the tools/ directory
- **Custom Prompts**: Define custom prompts in config/prompts.yaml
- **Model Configuration**: Configure LLM parameters in config/default.yaml