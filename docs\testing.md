# Testing Framework

This document describes the testing framework used in the project.

## Test Structure

Tests are organized in the `tests/` directory:

- **test_ollama_interface.py**: Tests for the Ollama interface
- **test_tool_functions.py**: Tests for the tool functions framework
- **test_transcript_tools.py**: Tests for transcript tools

## Running Tests

Tests can be run using the Python module syntax:

```bash
python -m test.test_ollama_interface
python -m test.test_tool_functions
python -m test.test_transcript_tools
```

## Test Logging

Test logs are stored in the `logs/` directory:

- **logs/test_ollama_interface.log**
- **logs/test_tool_functions.log**
- **logs/test_transcript_tools.log**

## Test Types

### Unit Tests

Test individual functions and classes in isolation.

```python
def test_add_numbers_tool():
    result = add_numbers_tool(1, 2)
    assert result == 3
```

### Integration Tests

Test interactions between components.

```python
def test_tool_execution():
    toolbox = LLM_Toolbox()
    toolbox.register(add_numbers_tool)
    result = toolbox.execute_tool_call({
        "name": "add_numbers",
        "arguments": {"a": 1, "b": 2}
    })
    assert result == 3
```

### End-to-End Tests

Test the complete workflow.

```python
def test_llm_tool_calling():
    chatbot = Chatbot(config)
    response = chatbot.chat("Add 1 and 2")
    assert "3" in response
```

## Test Data

Test data is stored in the `tests/data/` directory:

- **tests/data/transcripts/**: Sample transcripts for testing
- **tests/data/configs/**: Test configuration files

## Best Practices

1. **Isolation**: Tests should be isolated and not depend on each other
2. **Coverage**: Aim for high test coverage
3. **Mocking**: Use mocks for external dependencies
4. **Assertions**: Use specific assertions
5. **Documentation**: Document test purpose and expected behavior