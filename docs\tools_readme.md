# LLM Tools Framework

This document describes the tools framework used in our project for enabling LLM function calling capabilities.

## Core Components

### ParameterInfo

`ParameterInfo` is a dataclass that provides detailed information about function parameters for schema generation.

```python
@dataclass
class ParameterInfo:
    type: str = "string"  # JSON schema type
    description: str = None  # Parameter description
    enum: List[Any] = None  # Allowed values (for enums)
    default: Any = None  # Default value
    minimum: float = None  # Minimum value (for numbers)
    maximum: float = None  # Maximum value (for numbers)
    min_length: int = None  # Minimum length (for strings)
    max_length: int = None  # Maximum length (for strings)
    pattern: str = None  # Regex pattern (for strings)
    format: str = None  # Format hint (e.g., "date", "email")
```

### LLM_Tool

`LLM_Tool` represents a single function that can be called by the LLM through the Ollama tool API. It includes the function itself, its schema description, and validation utilities.

Key features:
- Automatic schema generation from function signatures
- Type inference from Python type hints
- Parameter validation
- Custom parameter descriptions and constraints

### LLM_Toolbox

`LLM_Toolbox` is a collection of `LLM_Tool` objects with utilities for registration, validation, and dispatching tool calls.

## Usage Examples

### Creating a Simple Tool

```python
from core.tool_functions import LLM_Tool, ParameterInfo

def add_numbers(a: int, b: int) -> int:
    """Add two numbers together"""
    return a + b

# Create a tool with automatic schema generation
add_tool = LLM_Tool(add_numbers)
```

### Adding Parameter Information

```python
from core.tool_functions import LLM_Tool, ParameterInfo

def draw_sine_wave(frequency: float, amplitude: float = 1.0, duration: float = 5.0):
    """Draw a sine wave with the given parameters"""
    # Implementation...

# Create a tool with detailed parameter information
sine_wave_tool = LLM_Tool(
    draw_sine_wave,
    description="Draw a sine wave with given frequency, amplitude and duration",
    param_info={
        "frequency": ParameterInfo(
            type="number",
            description="Frequency of the sine wave in Hz",
            minimum=0.1,
            maximum=10
        ),
        "amplitude": ParameterInfo(
            type="number",
            description="Amplitude of the sine wave",
            default=1,
            minimum=0.1,
            maximum=10
        ),
        "duration": ParameterInfo(
            type="number",
            description="Duration of the sine wave in seconds",
            minimum=1,
            maximum=10
        )
    },
    required_params=["frequency"]
)
```

### Using the Toolbox

```python
from core.tool_functions import LLM_Toolbox

# Create a toolbox
toolbox = LLM_Toolbox()

# Register tools
toolbox.register(add_numbers_tool)
toolbox.register(draw_sine_wave_tool)

# Register a function directly (will be converted to LLM_Tool)
def get_weather(location: str):
    """Get weather for a location"""
    # Implementation...

toolbox.register(
    get_weather,
    description="Get current weather for a location",
    param_info={"location": ParameterInfo(type="string", description="City or location name")}
)

# Get all tool schemas for the LLM
tool_schemas = toolbox.get_schemas()

# Execute a tool call from the LLM
result = toolbox.execute_tool_call({
    "name": "draw_sine_wave",
    "arguments": {"frequency": 2.5, "amplitude": 3.0}
})
```

## Adding New Tools

New tools should be added to the appropriate module in the `tools/` directory:

- `visualization_tools.py`: Tools for data visualization
- `transcript_tools.py`: Tools for transcript analysis
- `data_tools.py`: Tools for data manipulation

Each tool should:
1. Be a well-defined function with type hints
2. Have a clear docstring describing its purpose
3. Include appropriate parameter information using `ParameterInfo`
4. Be registered with the toolbox in the application setup

## Best Practices

1. **Keep tools focused**: Each tool should do one thing well
2. **Validate inputs**: Use `ParameterInfo` to define constraints
3. **Handle errors gracefully**: Catch exceptions and return meaningful error messages
4. **Document thoroughly**: Provide clear descriptions for tools and parameters
5. **Test independently**: Each tool should be testable in isolation

## Testing Tools

Use the test framework to verify tool functionality:

```python
# In tests/test_visualization_tools.py
def test_draw_sine_wave_tool():
    result = draw_sine_wave_tool(frequency=2.0, amplitude=1.5, duration=3.0)
    assert result["success"] is True
    assert "image_path" in result
```