2025-06-18 14:54:54,842 - __main__ - INFO - Starting application with config: Config:
	conversation_model: qwen3:0.6b
	embedding_model: qwen3:0.6b
	temperature: 0.2
	top_p: 0.9
	seed: 42
	func_sys_prompt: You are an assistant that may call tools but **only** when:
 • The user explicitly asks for current weather ➜ call get_weather
 • The user explicitly asks for a stock quote ➜ call get_stock_price
Otherwise answer directly without calling any tool.
If you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.If you don't call a tool, answer directly.
	ans_sys_prompt: You are an assistant that answers questions directly without calling any tool.
	dataset_path: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
	options: {'temperature': 0.2, 'top_p': 0.9, 'seed': 42}
	sys_prompt: {'func_sys_prompt': "You are an assistant that may call tools but **only** when:\n • The user explicitly asks for current weather ➜ call get_weather\n • The user explicitly asks for a stock quote ➜ call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.If you don't call a tool, answer directly.", 'ans_sys_prompt': 'You are an assistant that answers questions directly without calling any tool.'}
2025-06-18 14:54:54,844 - __main__ - INFO - Setting up toolbox with available tools
2025-06-18 14:54:54,844 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-18 14:54:54,844 - __main__ - INFO - Registered 1 tools
2025-06-18 14:54:54,844 - __main__ - INFO - Creating chatbot...
2025-06-18 14:54:54,845 - core.ollama_interface - INFO - Conversation history reset
2025-06-18 14:54:54,845 - core.ollama_interface - INFO - Initialized chatbot with model: qwen3:0.6b
2025-06-18 14:54:54,845 - __main__ - INFO - Starting conversation with history test...
2025-06-18 14:55:16,863 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-18 14:55:16,864 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-18 14:55:16,867 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001DC4944E750>
2025-06-18 14:55:16,868 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-18 14:55:16,868 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-18 14:55:16,868 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-18 14:55:16,869 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-18 14:55:16,870 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-18 14:55:19,886 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Wed, 18 Jun 2025 18:55:19 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-18 14:55:19,887 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-18 14:55:19,888 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-18 14:55:20,151 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-18 14:55:20,152 - httpcore.http11 - DEBUG - response_closed.started
2025-06-18 14:55:20,152 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-18 14:55:20,153 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content='Hello! How can I assist you today?' thinking=None images=None tool_calls=None
2025-06-18 14:55:54,519 - __main__ - INFO - Conversation with history test ended
2025-06-20 09:56:35,719 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 09:56:35,721 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 09:56:35,721 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 09:56:35,722 - __main__ - INFO - Registered 1 tools
2025-06-20 09:56:35,722 - __main__ - INFO - Creating chatbot...
2025-06-20 09:58:46,084 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 09:58:46,086 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 09:58:46,086 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 09:58:46,087 - __main__ - INFO - Registered 1 tools
2025-06-20 09:58:46,087 - __main__ - INFO - Creating chatbot...
2025-06-20 10:04:35,191 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 10:04:35,193 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 10:04:35,194 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 10:04:35,194 - __main__ - INFO - Registered 1 tools
2025-06-20 10:04:35,194 - __main__ - INFO - Creating chatbot...
2025-06-20 10:07:49,128 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 10:07:49,131 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 10:07:49,131 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 10:07:49,132 - __main__ - INFO - Registered 1 tools
2025-06-20 10:07:49,132 - __main__ - INFO - Creating chatbot...
2025-06-20 10:09:56,898 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 10:09:56,900 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 10:09:56,900 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 10:09:56,900 - __main__ - INFO - Registered 1 tools
2025-06-20 10:09:56,900 - __main__ - INFO - Creating chatbot...
2025-06-20 10:15:28,751 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default_prompt": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 10:15:28,752 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 10:15:28,753 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 10:15:28,753 - __main__ - INFO - Registered 1 tools
2025-06-20 10:15:28,754 - __main__ - INFO - Creating chatbot...
2025-06-20 10:18:51,237 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 10:18:51,239 - __main__ - INFO - Setting up toolbox with available tools
2025-06-20 10:18:51,239 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 10:18:51,240 - __main__ - INFO - Registered 1 tools
2025-06-20 10:18:51,240 - __main__ - INFO - Creating chatbot...
2025-06-20 10:18:51,240 - core.ollama_interface - INFO - Conversation history reset
2025-06-20 10:18:51,240 - core.ollama_interface - INFO - Initialized chatbot with model: qwen3:0.6b
2025-06-20 10:18:51,240 - __main__ - INFO - Starting conversation with history test...
2025-06-20 10:18:53,857 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-20 10:18:53,860 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:18:53,870 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000220742C6BD0>
2025-06-20 10:18:53,870 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:18:53,871 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:18:53,871 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:18:53,871 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:18:53,871 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:18:57,483 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:18:57 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:18:57,485 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:18:57,485 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:19:00,557 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:19:00,558 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:19:00,558 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:19:00,558 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content='<think>\nOkay, the user said "hello". I need to respond appropriately. Since they didn\'t ask for anything specific, I should greet them and offer help. Let me check if they need anything. Maybe they\'re interested in drawing a sine wave or something. But since they just said hello, a simple greeting and a prompt to ask if they need assistance is perfect. No need to call any functions here because there\'s no request involved.\n</think>\n\nHello! How can I assist you today? 😊' thinking=None images=None tool_calls=None
2025-06-20 10:19:33,729 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-20 10:19:33,731 - httpcore.connection - DEBUG - close.started
2025-06-20 10:19:33,731 - httpcore.connection - DEBUG - close.complete
2025-06-20 10:19:33,731 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:19:33,732 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002207457B4D0>
2025-06-20 10:19:33,732 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:19:33,733 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:19:33,733 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:19:33,733 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:19:33,734 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:19:33,970 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:19:33 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:19:33,970 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:19:33,971 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:19:34,659 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:19:34,660 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:19:34,660 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:19:34,660 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content="My name is a model, and I don't have a real name. However, I can assist you with various tasks. Let me know what you need! 😊" thinking=None images=None tool_calls=None
2025-06-20 10:20:28,619 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-20 10:20:28,620 - httpcore.connection - DEBUG - close.started
2025-06-20 10:20:28,620 - httpcore.connection - DEBUG - close.complete
2025-06-20 10:20:28,620 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:20:28,621 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002207457BF10>
2025-06-20 10:20:28,621 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:20:28,622 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:20:28,622 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:20:28,622 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:20:28,622 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:20:29,679 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:20:29 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:20:29,680 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:20:29,681 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:20:29,722 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:20:29,722 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:20:29,722 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:20:29,723 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content='' thinking=None images=None tool_calls=[ToolCall(function=Function(name='draw_sine_wave', arguments={'amplitude': 1, 'duration': 1, 'frequency': 1}))]
2025-06-20 10:20:29,723 - core.ollama_interface - INFO - Tool call: draw_sine_wave with args: {'amplitude': 1, 'duration': 1, 'frequency': 1}
2025-06-20 10:20:29,723 - core.tool_functions - INFO - The validated args: {'amplitude': 1.0, 'duration': 1.0, 'frequency': 1.0}
2025-06-20 10:20:35,858 - core.tool_functions - INFO - The result: Sine wave drawn by calling draw_sine_wave tool
2025-06-20 10:20:35,858 - core.ollama_interface - INFO - Tool result: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
2025-06-20 10:20:35,859 - httpcore.connection - DEBUG - close.started
2025-06-20 10:20:35,859 - httpcore.connection - DEBUG - close.complete
2025-06-20 10:20:35,859 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:20:35,860 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000022076704690>
2025-06-20 10:20:35,860 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:20:35,861 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:20:35,861 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:20:35,861 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:20:35,861 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:20:37,338 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:20:37 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:20:37,339 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:20:37,340 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:20:38,704 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:20:38,705 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:20:38,706 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:23:37,528 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-20 10:23:37,529 - httpcore.connection - DEBUG - close.started
2025-06-20 10:23:37,530 - httpcore.connection - DEBUG - close.complete
2025-06-20 10:23:37,530 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:23:37,531 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002207C000690>
2025-06-20 10:23:37,531 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:23:37,531 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:23:37,531 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:23:37,532 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:23:37,532 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:23:41,042 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:23:41 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:23:41,043 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:23:41,044 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:23:41,086 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:23:41,086 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:23:41,087 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:23:41,087 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content='' thinking=None images=None tool_calls=[ToolCall(function=Function(name='draw_sine_wave', arguments={'amplitude': 5, 'duration': 5, 'frequency': 5}))]
2025-06-20 10:23:41,087 - core.ollama_interface - INFO - Tool call: draw_sine_wave with args: {'amplitude': 5, 'duration': 5, 'frequency': 5}
2025-06-20 10:23:41,088 - core.tool_functions - INFO - The validated args: {'amplitude': 5.0, 'duration': 5.0, 'frequency': 5.0}
2025-06-20 10:23:44,151 - core.tool_functions - INFO - The result: Sine wave drawn by calling draw_sine_wave tool
2025-06-20 10:23:44,151 - core.ollama_interface - INFO - Tool result: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
2025-06-20 10:23:44,156 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:23:44,156 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:23:44,158 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:23:44,158 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:23:44,159 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:23:46,831 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:23:46 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:23:46,832 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:23:46,832 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:23:48,259 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:23:48,259 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:23:48,260 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:24:03,455 - core.ollama_interface - INFO - Running single turn with streaming output...
2025-06-20 10:24:03,456 - httpcore.connection - DEBUG - close.started
2025-06-20 10:24:03,457 - httpcore.connection - DEBUG - close.complete
2025-06-20 10:24:03,457 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=11434 local_address=None timeout=None socket_options=None
2025-06-20 10:24:03,458 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002207C02E7D0>
2025-06-20 10:24:03,458 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:24:03,458 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:24:03,458 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:24:03,459 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:24:03,459 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:24:07,282 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:24:07 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:24:07,284 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:24:07,284 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:24:12,341 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:24:12,342 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:24:12,342 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:24:12,342 - core.ollama_interface - DEBUG - Final combined chunk: role='assistant' content='<think>\nOkay, the user is asking to draw a sine wave with parameters amplitude 8, duration 8, and frequency 8. Let me check the tools available. The provided function is draw_sine_wave, which requires amplitude, duration, and frequency. All three parameters are given here. So I need to call the function with those values. The user might be testing if the tool can handle these parameters, so I should generate the tool call with amplitude 8, duration 8, and frequency 8. No need to mention anything else since all required parameters are provided.\n</think>\n\n' thinking=None images=None tool_calls=[ToolCall(function=Function(name='draw_sine_wave', arguments={'amplitude': 8, 'duration': 8, 'frequency': 8}))]
2025-06-20 10:24:12,343 - core.ollama_interface - INFO - Tool call: draw_sine_wave with args: {'amplitude': 8, 'duration': 8, 'frequency': 8}
2025-06-20 10:24:12,343 - core.tool_functions - INFO - The validated args: {'amplitude': 8.0, 'duration': 8.0, 'frequency': 8.0}
2025-06-20 10:24:15,661 - core.tool_functions - INFO - The result: Sine wave drawn by calling draw_sine_wave tool
2025-06-20 10:24:15,662 - core.ollama_interface - INFO - Tool result: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
2025-06-20 10:24:15,664 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-20 10:24:15,669 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-20 10:24:15,672 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-20 10:24:15,673 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-20 10:24:15,673 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-20 10:24:20,515 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/x-ndjson'), (b'Date', b'Fri, 20 Jun 2025 14:24:20 GMT'), (b'Transfer-Encoding', b'chunked')])
2025-06-20 10:24:20,516 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-20 10:24:20,517 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-20 10:24:24,960 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-20 10:24:24,961 - httpcore.http11 - DEBUG - response_closed.started
2025-06-20 10:24:24,961 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-20 10:24:44,223 - __main__ - INFO - Conversation with history test ended
