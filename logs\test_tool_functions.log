2025-06-20 09:55:54,160 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 09:55:54,161 - core.tool_functions - INFO - Registered tool: add_two_numbers - Add two integer numbers together
2025-06-20 09:55:54,161 - core.tool_functions - INFO - Registered tool: get_weather - Get current weather for a location
2025-06-20 09:55:54,161 - core.tool_functions - INFO - Registered tool: subtract_numbers - Subtract two numbers
2025-06-20 09:55:54,162 - core.tool_functions - INFO - Registered tool: multiply_numbers - Multiply two numbers
2025-06-20 09:55:54,163 - core.tool_functions - INFO - Registered tool: draw_sine_wave - Draw a sine wave with given frequency, amplitude and duration using matplotlib and numpy
2025-06-20 09:55:54,173 - core.tool_functions - DEBUG - Printing schemas for all registered tools:
[
    {
        "type": "function",
        "function": {
            "name": "add_two_numbers",
            "description": "Add two integer numbers together",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "integer",
                        "description": "First number to add",
                        "minimum": 0,
                        "maximum": 1000
                    },
                    "b": {
                        "type": "integer",
                        "description": "Second number to add",
                        "minimum": 0,
                        "maximum": 1000
                    }
                },
                "required": [
                    "a"
                ]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get current weather for a location",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "City name",
                        "min_length": 2
                    },
                    "country": {
                        "type": "string",
                        "description": "Country code",
                        "enum": [
                            "US",
                            "CA",
                            "GB",
                            "DE",
                            "FR"
                        ]
                    },
                    "units": {
                        "type": "string",
                        "description": "Temperature units",
                        "enum": [
                            "metric",
                            "imperial"
                        ],
                        "default": "metric"
                    }
                },
                "required": [
                    "city",
                    "country"
                ]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "subtract_numbers",
            "description": "Subtract two numbers",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "integer",
                        "description": "First number"
                    },
                    "b": {
                        "type": "integer",
                        "description": "Second number to subtract from the first"
                    }
                },
                "required": [
                    "a",
                    "b"
                ]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "multiply_numbers",
            "description": "Multiply two numbers",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "integer",
                        "description": "First number"
                    },
                    "b": {
                        "type": "integer",
                        "description": "Second number to multiply with the first"
                    }
                },
                "required": [
                    "a",
                    "b"
                ]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "draw_sine_wave",
            "description": "Draw a sine wave with given frequency, amplitude and duration using matplotlib and numpy",
            "parameters": {
                "type": "object",
                "properties": {
                    "frequency": {
                        "type": "number",
                        "description": "Frequency of the sine wave in Hz",
                        "minimum": 0.1,
                        "maximum": 10
                    },
                    "amplitude": {
                        "type": "number",
                        "description": "Amplitude of the sine wave",
                        "default": 1,
                        "minimum": 0.1,
                        "maximum": 10
                    },
                    "duration": {
                        "type": "number",
                        "description": "Duration of the sine wave in seconds",
                        "minimum": 1,
                        "maximum": 10
                    }
                },
                "required": [
                    "frequency",
                    "duration"
                ]
            }
        }
    }
]
2025-06-20 09:55:54,185 - __main__ - INFO - 
Result of add_two_numbers: {'error': 'Validation error: Parameter b must be <= 1000, got 300000'}
2025-06-20 09:55:54,186 - __main__ - INFO - 
Weather result: {'error': "Validation error: Parameter country must be one of ['US', 'CA', 'GB', 'DE', 'FR'], got GdfB"}
2025-06-20 09:55:54,186 - core.tool_functions - INFO - The validated args: {'city': 'London', 'country': <Country.GB: 'GB'>, 'units': <WeatherUnits.IMPERIAL: 'imperial'>}
2025-06-20 09:55:54,187 - core.tool_functions - INFO - The result: {'temperature': 22, 'conditions': 'sunny', 'city': 'London', 'country': 'GB', 'units': 'imperial'}
2025-06-20 09:55:54,187 - __main__ - INFO - 
Weather result: {'result': {'temperature': 22, 'conditions': 'sunny', 'city': 'London', 'country': 'GB', 'units': 'imperial'}}
2025-06-20 09:55:54,188 - __main__ - INFO - 
Result with missing parameter: {'error': 'Validation error: Missing required parameter: country'}
2025-06-20 09:55:54,188 - __main__ - INFO - 
Result with restricted tools: {'error': 'Tool get_weather not permitted in this context.'}
2025-06-20 09:55:54,188 - core.tool_functions - INFO - The validated args: {'frequency': 3.0, 'duration': 7.0, 'amplitude': 1.0}
2025-06-20 09:55:58,213 - core.tool_functions - INFO - The result: Sine wave drawn by calling draw_sine_wave tool
2025-06-20 09:55:58,214 - __main__ - INFO - 
Result of draw_sine_wave: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
