2025-06-20 11:07:46,713 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/<PERSON><PERSON><PERSON>/Validated Transcripts
2025-06-20 11:07:46,714 - __main__ - INFO - Testing Participant...
2025-06-20 13:49:37,376 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/<PERSON><PERSON><PERSON>/Validated Transcripts
2025-06-20 13:49:37,377 - __main__ - INFO - Testing Participant...
2025-06-20 13:53:27,660 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 13:53:27,661 - __main__ - INFO - Testing Participant...
2025-06-20 13:54:13,863 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao <PERSON>/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 13:54:13,865 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 13:54:13,865 - __main__ - INFO - Testing Participant...
2025-06-20 13:56:11,313 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 13:56:11,314 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 13:56:11,315 - __main__ - INFO - Testing Participant...
2025-06-20 13:56:11,315 - __main__ - INFO - Starting test 1...
2025-06-20 13:56:11,316 - __main__ - INFO - Ending test 1...
2025-06-20 13:57:53,953 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 13:57:53,964 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 13:57:53,966 - __main__ - INFO - Testing Participant...
2025-06-20 13:57:53,966 - __main__ - INFO - Starting test 1...
2025-06-20 13:57:54,089 - __main__ - INFO - Ending test 1...
2025-06-20 14:04:06,021 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:04:06,022 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:04:06,024 - __main__ - INFO - Testing Participant...
2025-06-20 14:04:06,024 - __main__ - INFO - Starting test 1...
2025-06-20 14:04:06,025 - core.transcript_parsing - INFO - Transcript: (WindowsPath('C:/Users/<USER>/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts/Tesla/2016k_154/2016k_154 Stage 3 Interview audio for validation.docx'), '2016k_154 Stage 3 Interview audio for validation.docx', '2016k_154', 'Tesla', 3)
2025-06-20 14:04:06,129 - __main__ - INFO - Ending test 1...
2025-06-20 14:04:28,443 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:04:28,444 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:04:28,445 - __main__ - INFO - Testing Participant...
2025-06-20 14:04:28,445 - __main__ - INFO - Starting test 1...
2025-06-20 14:04:28,445 - core.transcript_parsing - INFO - (WindowsPath('C:/Users/<USER>/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts/Tesla/2016k_154/2016k_154 Stage 3 Interview audio for validation.docx'), '2016k_154 Stage 3 Interview audio for validation.docx', '2016k_154', 'Tesla', 3)
2025-06-20 14:04:28,494 - __main__ - INFO - Ending test 1...
2025-06-20 14:27:55,134 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:27:55,139 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:27:55,143 - __main__ - INFO - Testing Participant...
2025-06-20 14:27:55,144 - __main__ - INFO - Starting test 1...
2025-06-20 14:28:28,633 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:28:28,635 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:28:28,635 - __main__ - INFO - Testing Participant...
2025-06-20 14:28:28,636 - __main__ - INFO - Starting test 1...
2025-06-20 14:29:15,682 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:29:15,684 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:29:15,685 - __main__ - INFO - Testing Participant...
2025-06-20 14:29:15,685 - __main__ - INFO - Starting test 1...
2025-06-20 14:30:46,197 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:30:46,199 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:30:46,200 - __main__ - INFO - Testing Participant...
2025-06-20 14:30:46,200 - __main__ - INFO - Starting test 1...
2025-06-20 14:30:46,301 - __main__ - INFO - Ending test 1...
2025-06-20 14:33:24,201 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:33:24,203 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:33:24,204 - __main__ - INFO - Testing Participant...
2025-06-20 14:33:24,204 - __main__ - INFO - Starting test 1...
2025-06-20 14:33:24,258 - __main__ - INFO - Ending test 1...
2025-06-20 14:36:50,436 - __main__ - INFO - Starting application with config: Config:
llm_params: {
  "conversation_model": "qwen3:0.6b",
  "embedding_model": "qwen3:0.6b",
  "options": {
    "temperature": 0.3,
    "seed": 42,
    "top_p": 0.9
  },
  "think": true
}
dataset_params: {
  "path": "C:\\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts"
}
prompt_pools: {
  "sys_prompts": {
    "default": "",
    "function_calling": "You are an assistant that may call tools but **only** when:\n \u00e2\u20ac\u00a2 The user explicitly asks for current weather \u00e2\u017e\u0153 call get_weather\n \u00e2\u20ac\u00a2 The user explicitly asks for a stock quote \u00e2\u017e\u0153 call get_stock_price\nOtherwise answer directly without calling any tool.\nIf you do call a tool, call **exactly one** per turn and include all required parameters. Never hallucinate arguments.\nIf you don't call a tool, answer directly.\n",
    "question_answering": "You are an assistant that answers questions directly without calling any tool.\n"
  },
  "user_prompts": {
    "extract_metadata": "Extract the metadata from the following text:\n{text}\n",
    "extract_qa_pairs": "Extract the question and answer pairs from the following text:\n{text}\n",
    "extract_qa_pairs_from_transcript": "Extract the question and answer pairs from the following transcript:\n{text}\n",
    "extract_qa_pairs_from_transcript_with_metadata": "Extract the question and answer pairs from the following transcript, given the following metadata:\n{metadata}\n{text}"
  }
}
2025-06-20 14:36:50,438 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:36:50,438 - __main__ - INFO - Testing Participant...
2025-06-20 14:36:50,438 - __main__ - INFO - Starting test 1...
2025-06-20 14:36:50,488 - __main__ - INFO - Ending test 1...
2025-06-20 14:42:04,916 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:42:04,918 - __main__ - INFO - Testing Participant...
2025-06-20 14:42:04,918 - __main__ - INFO - Starting test 1...
2025-06-20 14:42:05,071 - __main__ - INFO - Ending test 1...
2025-06-20 14:42:28,266 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:42:28,269 - __main__ - INFO - Testing Participant...
2025-06-20 14:42:28,269 - __main__ - INFO - Starting test 1...
2025-06-20 14:42:28,366 - __main__ - INFO - Ending test 1...
2025-06-20 14:44:46,130 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:44:46,132 - __main__ - INFO - Testing Participant...
2025-06-20 14:44:46,132 - __main__ - INFO - Starting test 1...
2025-06-20 14:44:46,134 - core.transcript_parsing - DEBUG - Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
, File name: 2016k_154 Stage 3 Interview audio for validation.docx
, Participant ID: 2016k_154
, Vehicle Type: Tesla
, Interview Stage: 3
2025-06-20 14:44:46,392 - __main__ - INFO - Ending test 1...
2025-06-20 14:45:15,551 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:45:15,553 - __main__ - INFO - Testing Participant...
2025-06-20 14:45:15,553 - __main__ - INFO - Starting test 1...
2025-06-20 14:45:15,554 - core.transcript_parsing - DEBUG - Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
 File name: 2016k_154 Stage 3 Interview audio for validation.docx,
 Participant ID: 2016k_154,
 Vehicle Type: Tesla,
 Interview Stage: 3
2025-06-20 14:45:15,619 - __main__ - INFO - Ending test 1...
2025-06-20 14:45:58,882 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:45:58,883 - __main__ - INFO - Testing Participant...
2025-06-20 14:45:58,883 - __main__ - INFO - Starting test 1...
2025-06-20 14:45:58,884 - core.transcript_parsing - DEBUG - Path Parsing:
 Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
 File name: 2016k_154 Stage 3 Interview audio for validation.docx,
 Participant ID: 2016k_154,
 Vehicle Type: Tesla,
 Interview Stage: 3
2025-06-20 14:45:58,968 - __main__ - INFO - Ending test 1...
2025-06-20 14:46:34,367 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 14:46:34,369 - __main__ - INFO - Testing Participant...
2025-06-20 14:46:34,369 - __main__ - INFO - Starting test 1...
2025-06-20 14:46:34,369 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 14:46:34,449 - __main__ - INFO - Ending test 1...
2025-06-20 15:12:02,512 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 15:12:02,513 - __main__ - INFO - Testing Participant...
2025-06-20 15:12:02,514 - __main__ - INFO - Starting test 1...
2025-06-20 15:12:02,515 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 15:16:35,070 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 15:16:35,072 - __main__ - INFO - Testing Participant...
2025-06-20 15:16:35,072 - __main__ - INFO - Starting test 1...
2025-06-20 15:16:35,073 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 15:48:50,520 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 15:48:50,522 - __main__ - INFO - Testing Participant...
2025-06-20 15:48:50,522 - __main__ - INFO - Starting test 1...
2025-06-20 15:50:45,188 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 15:50:45,189 - __main__ - INFO - Testing Participant...
2025-06-20 15:50:45,191 - __main__ - INFO - Starting test 1...
2025-06-20 15:53:25,516 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 15:53:25,517 - __main__ - INFO - Testing Participant...
2025-06-20 15:53:25,518 - __main__ - INFO - Starting test 1...
2025-06-20 15:53:25,518 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:04:41,128 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:04:41,129 - __main__ - INFO - Testing Participant...
2025-06-20 16:04:41,129 - __main__ - INFO - Starting test 1...
2025-06-20 16:04:41,129 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:05:17,215 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:05:17,216 - __main__ - INFO - Testing Participant...
2025-06-20 16:05:17,216 - __main__ - INFO - Starting test 1...
2025-06-20 16:05:17,216 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:05:17,261 - __main__ - INFO - Ending test 1...
2025-06-20 16:05:52,038 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:05:52,040 - __main__ - INFO - Testing Participant...
2025-06-20 16:05:52,040 - __main__ - INFO - Starting test 1...
2025-06-20 16:05:52,041 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:06:36,221 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:06:36,222 - __main__ - INFO - Testing Participant...
2025-06-20 16:06:36,223 - __main__ - INFO - Starting test 1...
2025-06-20 16:06:36,224 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:06:36,276 - __main__ - INFO - Ending test 1...
2025-06-20 16:06:55,907 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:06:55,909 - __main__ - INFO - Testing Participant...
2025-06-20 16:06:55,910 - __main__ - INFO - Starting test 1...
2025-06-20 16:06:55,912 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:06:56,021 - __main__ - INFO - Ending test 1...
2025-06-20 16:08:15,135 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:08:15,136 - __main__ - INFO - Testing Participant...
2025-06-20 16:08:15,136 - __main__ - INFO - Starting test 1...
2025-06-20 16:08:15,137 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:08:15,184 - __main__ - INFO - Ending test 1...
2025-06-20 16:10:48,584 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:10:48,586 - __main__ - INFO - Testing Participant...
2025-06-20 16:10:48,586 - __main__ - INFO - Starting test 1...
2025-06-20 16:10:48,587 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:10:48,645 - __main__ - INFO - Ending test 1...
2025-06-20 16:11:18,429 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:11:18,430 - __main__ - INFO - Testing Participant...
2025-06-20 16:11:18,431 - __main__ - INFO - Starting test 1...
2025-06-20 16:11:18,431 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:11:18,477 - __main__ - INFO - Ending test 1...
2025-06-20 16:13:26,284 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:13:26,285 - __main__ - INFO - Testing Participant...
2025-06-20 16:13:26,286 - __main__ - INFO - Starting test 1...
2025-06-20 16:13:26,287 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:13:26,340 - __main__ - INFO - Ending test 1...
2025-06-20 16:14:19,990 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:14:19,992 - __main__ - INFO - Testing Participant...
2025-06-20 16:14:19,993 - __main__ - INFO - Starting test 1...
2025-06-20 16:14:19,993 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:15:46,238 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:15:46,240 - __main__ - INFO - Testing Participant...
2025-06-20 16:15:46,241 - __main__ - INFO - Starting test 1...
2025-06-20 16:15:46,241 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:17:11,736 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:17:11,737 - __main__ - INFO - Testing Participant...
2025-06-20 16:17:11,737 - __main__ - INFO - Starting test 1...
2025-06-20 16:17:11,738 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:17:11,802 - __main__ - INFO - Ending test 1...
2025-06-20 16:19:44,076 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:19:44,077 - __main__ - INFO - Testing Participant...
2025-06-20 16:19:44,078 - __main__ - INFO - Starting test 1...
2025-06-20 16:19:44,078 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:19:44,136 - __main__ - INFO - Ending test 1...
2025-06-20 16:20:58,109 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:20:58,111 - __main__ - INFO - Testing Participant...
2025-06-20 16:20:58,111 - __main__ - INFO - Starting test 1...
2025-06-20 16:20:58,112 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:20:58,171 - __main__ - INFO - Ending test 1...
2025-06-20 16:22:07,721 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:22:07,723 - __main__ - INFO - Testing Participant...
2025-06-20 16:22:07,723 - __main__ - INFO - Starting test 1...
2025-06-20 16:22:07,723 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:22:07,778 - __main__ - INFO - Ending test 1...
2025-06-20 16:27:50,897 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:27:50,899 - __main__ - INFO - Testing Participant...
2025-06-20 16:27:50,900 - __main__ - INFO - Starting test 1...
2025-06-20 16:27:50,900 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:27:50,972 - __main__ - INFO - Ending test 1...
2025-06-20 16:28:18,003 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:28:18,003 - __main__ - INFO - Testing Participant...
2025-06-20 16:28:18,004 - __main__ - INFO - Starting test 1...
2025-06-20 16:28:18,004 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:28:18,077 - __main__ - INFO - Ending test 1...
2025-06-20 16:28:55,836 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:28:55,837 - __main__ - INFO - Testing Participant...
2025-06-20 16:28:55,837 - __main__ - INFO - Starting test 1...
2025-06-20 16:28:55,838 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:28:55,895 - __main__ - INFO - Ending test 1...
2025-06-20 16:30:01,986 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:30:01,988 - __main__ - INFO - Testing Participant...
2025-06-20 16:30:01,988 - __main__ - INFO - Starting test 1...
2025-06-20 16:30:01,989 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:30:02,045 - __main__ - INFO - Ending test 1...
2025-06-20 16:36:05,907 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-20 16:36:05,908 - __main__ - INFO - Testing Participant...
2025-06-20 16:36:05,908 - __main__ - INFO - Starting test 1...
2025-06-20 16:36:05,909 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-20 16:36:05,955 - __main__ - INFO - Ending test 1...
2025-06-23 10:07:36,071 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 10:07:36,072 - __main__ - INFO - Testing Participant...
2025-06-23 10:07:36,073 - __main__ - INFO - Starting test 1...
2025-06-23 10:07:36,073 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 10:07:36,138 - __main__ - INFO - Ending test 1...
2025-06-23 11:20:02,551 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:20:02,552 - __main__ - INFO - Testing Participant...
2025-06-23 11:20:02,552 - __main__ - INFO - Starting test 1...
2025-06-23 11:20:02,552 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:20:02,587 - core.transcript_parsing - DEBUG - Header para end at 22 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
2025-06-23 11:20:02,588 - core.transcript_parsing - DEBUG - header_para_end_idx: 22
2025-06-23 11:20:20,994 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:20:20,995 - __main__ - INFO - Testing Participant...
2025-06-23 11:20:20,996 - __main__ - INFO - Starting test 1...
2025-06-23 11:20:20,997 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:20:21,037 - core.transcript_parsing - DEBUG - Header para end at 22 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
2025-06-23 11:20:21,039 - core.transcript_parsing - DEBUG - header_para_end_idx: 22
2025-06-23 11:20:21,072 - __main__ - INFO - Ending test 1...
2025-06-23 11:38:50,465 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:38:50,465 - __main__ - INFO - Testing Participant...
2025-06-23 11:38:50,465 - __main__ - INFO - Starting test 1...
2025-06-23 11:38:50,466 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:38:50,497 - core.transcript_parsing - DEBUG - Header para end at 22 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
2025-06-23 11:38:50,498 - core.transcript_parsing - DEBUG - header_para_end_idx: 22
2025-06-23 11:38:50,522 - __main__ - INFO - Ending test 1...
2025-06-23 11:43:36,054 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:43:36,056 - __main__ - INFO - Testing Participant...
2025-06-23 11:43:36,056 - __main__ - INFO - Starting test 1...
2025-06-23 11:43:36,057 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:43:36,100 - core.transcript_parsing - DEBUG - Header para end at 22 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
2025-06-23 11:43:36,101 - core.transcript_parsing - DEBUG - header_para_end_idx: 22
2025-06-23 11:43:36,134 - __main__ - INFO - Ending test 1...
2025-06-23 11:44:17,007 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:44:17,008 - __main__ - INFO - Testing Participant...
2025-06-23 11:44:17,009 - __main__ - INFO - Starting test 1...
2025-06-23 11:44:17,010 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:44:17,060 - core.transcript_parsing - DEBUG - Header para end at 22 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx
2025-06-23 11:44:17,061 - core.transcript_parsing - DEBUG - header_para_end_idx: 22
2025-06-23 11:44:17,096 - __main__ - INFO - Ending test 1...
2025-06-23 11:49:48,130 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:49:48,131 - __main__ - INFO - Testing Participant...
2025-06-23 11:49:48,132 - __main__ - INFO - Starting test 1...
2025-06-23 11:49:48,132 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:49:48,171 - core.transcript_parsing - DEBUG - Header para end at 31 with [required] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx with NA
2025-06-23 11:49:48,172 - core.transcript_parsing - DEBUG - header_para_end_idx: 31
2025-06-23 11:49:48,197 - __main__ - INFO - Ending test 1...
2025-06-23 11:51:10,683 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:51:10,684 - __main__ - INFO - Testing Participant...
2025-06-23 11:51:10,684 - __main__ - INFO - Starting test 1...
2025-06-23 11:51:10,685 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:51:10,718 - core.transcript_parsing - DEBUG - Header para end at 31 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx with NA
2025-06-23 11:51:10,719 - core.transcript_parsing - DEBUG - header_para_end_idx: 31
2025-06-23 11:51:10,741 - __main__ - INFO - Ending test 1...
2025-06-23 11:52:47,996 - __main__ - INFO - Transcript dataset dir: C:\Users/zhouqiao/AgeLab Dropbox/Zhouqiao Zhao/Validated Transcripts
2025-06-23 11:52:47,997 - __main__ - INFO - Testing Participant...
2025-06-23 11:52:47,997 - __main__ - INFO - Starting test 1...
2025-06-23 11:52:47,998 - core.transcript_parsing - DEBUG - Path Parsing:
	Path: C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx,
	File name: 2016k_154 Stage 3 Interview audio for validation.docx,
	Participant ID: 2016k_154,
	Vehicle Type: Tesla,
	Interview Stage: 3
2025-06-23 11:52:48,036 - core.transcript_parsing - DEBUG - Header para end at 31 with [all] metadata found in C:\Users\<USER>\AgeLab Dropbox\Zhouqiao Zhao\Validated Transcripts\Tesla\2016k_154\2016k_154 Stage 3 Interview audio for validation.docx [with] NA
2025-06-23 11:52:48,037 - core.transcript_parsing - DEBUG - header_para_end_idx: 31
2025-06-23 11:52:48,059 - __main__ - INFO - Ending test 1...
