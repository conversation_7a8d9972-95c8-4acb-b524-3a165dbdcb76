2025-06-20 09:56:18,117 - core.tool_functions - INFO - Initializing LLM_Toolbox...
2025-06-20 09:56:18,119 - core.tool_functions - DEBUG - Printing schemas for all registered tools:
[
    {
        "type": "function",
        "function": {
            "name": "draw_sine_wave",
            "description": "Draw a sine wave with given frequency, amplitude and duration using matplotlib and numpy",
            "parameters": {
                "type": "object",
                "properties": {
                    "frequency": {
                        "type": "number",
                        "description": "Frequency of the sine wave in Hz",
                        "minimum": 0.1,
                        "maximum": 10
                    },
                    "amplitude": {
                        "type": "number",
                        "description": "Amplitude of the sine wave",
                        "default": 1,
                        "minimum": 0.1,
                        "maximum": 10
                    },
                    "duration": {
                        "type": "number",
                        "description": "Duration of the sine wave in seconds",
                        "minimum": 1,
                        "maximum": 10
                    }
                },
                "required": [
                    "frequency",
                    "duration"
                ]
            }
        }
    }
]
2025-06-20 09:56:18,122 - core.tool_functions - INFO - Registered tool: draw_sine_wave - Draw a sine wave with given frequency, amplitude and duration using matplotlib and numpy
2025-06-20 09:56:18,122 - core.tool_functions - INFO - The validated args: {'frequency': 3.0, 'duration': 7.0, 'amplitude': 1.0}
2025-06-20 09:56:22,313 - core.tool_functions - INFO - The result: Sine wave drawn by calling draw_sine_wave tool
2025-06-20 09:56:22,318 - __main__ - INFO - Result: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
2025-06-20 09:56:22,318 - __main__ - INFO - Result: {'result': 'Sine wave drawn by calling draw_sine_wave tool'}
