from utils.config import parse_args, print_config_help
from utils.logger_setup import setup_logging
from core.ollama_interface import Chatbot
from core.tool_functions import LLM_Toolbox
from tools.visualization_tools import draw_sine_wave_tool
from tests.test_ollama_interface import test_single_turn, test_single_turn_streaming
import logging

# Set up logging
setup_logging(
    level=logging.INFO,
    log_file="logs/test.log",  # Optional: log to file
    console=True
)
logger = logging.getLogger(__name__)


def setup_toolbox():
    """Create and configure the toolbox with all available tools"""
    logger.info("Setting up toolbox with available tools")
    toolbox = LLM_Toolbox()
    
    # Register visualization tools
    toolbox.register(draw_sine_wave_tool)
    # toolbox.register(...)
    
    logger.info(f"Registered {len(toolbox.tools)} tools")
    return toolbox

def main():
    cfg = parse_args()
    logger.info(f"Starting application with config:\n{cfg}")
    print_config_help()
    
    # Set up tools
    toolbox = setup_toolbox()
    
    # Create chatbot with tools
    logger.info("Creating chatbot...")
    chatbot = Chatbot(
        model=cfg.conversation_model,
        system_prompt_pool=cfg.sys_prompt,
        options=cfg.options,
        toolbox=toolbox
    )

    # Run tests
    test_single_turn(chatbot)
    # test_single_turn_streaming(chatbot)
    
    # Start conversation
    # logger.info("Starting conversation...")
    # chatbot.reset_messages()
    # chatbot.convertation_with_history()
    # logger.info("Conversation ended")

   
if __name__ == '__main__':
    main()