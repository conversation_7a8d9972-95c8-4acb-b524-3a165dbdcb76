from utils.config import parse_args, print_config_help
from utils.logger_setup import setup_logging
from core.ollama_interface import Cha<PERSON>bot
from core.tool_functions import LLM_Toolbox
from tools.visualization_tools import draw_sine_wave_tool
import logging

# Set up logging
setup_logging(
    level=logging.DEBUG,
    log_file="logs/test_ollama_interface.log",  # Optional: log to file
    console=True
)
logger = logging.getLogger(__name__)

def setup_toolbox():
    """Create and configure the toolbox with all available tools"""
    logger.info("Setting up toolbox with available tools")
    toolbox = LLM_Toolbox()
    
    # Register visualization tools
    toolbox.register(draw_sine_wave_tool)
    # toolbox.register(...)
    
    logger.info(f"Registered {len(toolbox.tools)} tools")
    return toolbox

######## Test Cases ########
def test_single_turn(chatbot):
    logger.info("Starting single turn test...")
    
    # chatbot.single_turn('What is the weather in New York?')
    # chatbot.single_turn('What is the weather in Boston?')
    # chatbot.single_turn('What are the two questions I asked?')
    chatbot.single_turn('Draw a sine wave with frequency 3 and duration 7')
    chatbot.single_turn()
    
    chatbot.print_conversation_history()
    
    logger.info("Single turn test ended")

def test_single_turn_streaming(chatbot):
    logger.info("Starting single turn streaming test...")

    # chatbot.single_turn_streaming('What is the weather in New York?')
    # chatbot.switch_think()
    # chatbot.print_think_mode()
    
    # chatbot.single_turn_streaming('Draw a sine wave with frequency 3, duration 7, and amplitude 2')
    chatbot.single_turn_streaming('Draw a sine wave with frequency 3, duration 7')
    # chatbot.single_turn_streaming()
    
    # chatbot.print_questions_history()
    # chatbot.print_answers_history()
    chatbot.print_conversation_history()
    
    logger.info("Single turn streaming test ended")

def test_conversation_with_history(chatbot):
    logger.info("Starting conversation with history test...")
    
    chatbot.convertation_with_history()
    
    logger.info("Conversation with history test ended")

def main():
    cfg = parse_args()
    logger.info(f"Starting application with config: {cfg}")
    print_config_help()
    
    # Set up tools
    toolbox = setup_toolbox()
    
    # Create chatbot with tools
    logger.info("Creating chatbot...")
    chatbot = Chatbot(
        model_params=cfg.llm_params, 
        prompt_pool=cfg.prompt_pools,
        toolbox=toolbox
    )
    
    # test_single_turn(chatbot)
    # test_single_turn_streaming(chatbot)
    test_conversation_with_history(chatbot)

if __name__ == '__main__':
    main()
