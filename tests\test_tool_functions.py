from utils.logger_setup import setup_logging
from core.tool_functions import LLM_Toolbox, ParameterInfo
from tools.visualization_tools import draw_sine_wave_tool
from typing import Dict, Any
from enum import Enum
import logging

# Set up logging
setup_logging(
    level=logging.DEBUG,
    log_file="logs/test_tool_functions.log",  # Optional: log to file
    console=True
)
logger = logging.getLogger(__name__)

########## Example tool functions ##########
def add_two_numbers(a: int, b: int = 0):
    return a + b

def multiply_numbers(a: int, b: int):
    return a * b

class WeatherUnits(str, Enum):
    METRIC = "metric"
    IMPERIAL = "imperial"
class Country(str, Enum):
    US = "US"
    CA = "CA"
    GB = "GB"
    DE = "DE"
    FR = "FR"
def get_weather(city: str, country: Country, units: WeatherUnits = WeatherUnits.METRIC) -> Dict[str, Any]:
    """Get the current weather for a city"""
    # Pseudo API call
    return {
        "temperature": 22,
        "conditions": "sunny",
        "city": city,
        "country": country.value,
        "units": units.value
    }


def test_case_1():
    ########## Create toolbox ##########
    toolbox = LLM_Toolbox()
    
    ########## Register test tools ##########
    # tool 1
    toolbox.register(
        add_two_numbers,
        description="Add two integer numbers together",
        param_info={
            "a": ParameterInfo(
                type="integer",
                description="First number to add",
                minimum=0,
                maximum=1000
            ),
            "b": ParameterInfo(
                type="integer",
                description="Second number to add",
                minimum=0,
                maximum=1000
            )
        }
    )
    
    # tool 2
    toolbox.register(
        get_weather, 
        description="Get current weather for a location",
        param_info={
            "city": {
                "type": "string",
                "description": "City name",
                "min_length": 2
            },
            "country": {
                "type": "string",
                "description": "Country code",
                "enum": ["US", "CA", "GB", "DE", "FR"]
            },
            "units": ParameterInfo(
                type="string",
                description="Temperature units",
                enum=["metric", "imperial"],
                default="metric"
            )
        }
    )
    
    # tool 3
    @toolbox.register_decorator(
        description="Subtract two numbers",
        param_info={
            "a": ParameterInfo(type="integer", description="First number"),
            "b": ParameterInfo(type="integer", description="Second number to subtract from the first")
        }
    )
    def subtract_numbers(a: int, b: int) -> int:
        return a - b
    
    # tool 4
    multiply_numbers_tool = toolbox.register(
        multiply_numbers,
        description="Multiply two numbers",
        param_info={
            "a": ParameterInfo(type="integer", description="First number"),
            "b": ParameterInfo(type="integer", description="Second number to multiply with the first")
        }
    )
    toolbox.register(multiply_numbers_tool)
    
    # tool 5
    toolbox.register(draw_sine_wave_tool)
    
    # Print available tool schemas
    toolbox.print_available_tools()
    toolbox.print_schemas()
    
    ########## Example dispatch ##########
    # Example with type conversion
    result = toolbox.safe_dispatch({
        "name": "add_two_numbers",
        "arguments": {"a": 5, "b": 300000}
    })
    logger.info(f"\nResult of add_two_numbers: {result}")
    
    # Example with enum
    weather_result = toolbox.safe_dispatch({
        "name": "get_weather",
        "arguments": {"city": "London", "country": "GdfB", "units": "imperial"}
    })
    logger.info(f"\nWeather result: {weather_result}")
    
    weather_result = toolbox.safe_dispatch({
        "name": "get_weather",
        "arguments": {"city": "London", "country": "GB", "units": "imperial"}
    })
    logger.info(f"\nWeather result: {weather_result}")
    
    # Example with error
    error_result = toolbox.safe_dispatch({
        "name": "get_weather",
        "arguments": {"city": "London"}  # Missing required country parameter
    })
    logger.info(f"\nResult with missing parameter: {error_result}")
    
    # Example with selected tools
    selected_tools = ["add_two_numbers"]
    result = toolbox.safe_dispatch({
        "name": "get_weather",
        "arguments": {"city": "London", "country": "GB", "units": "imperial"}
    }, selected_tools)
    logger.info(f"\nResult with restricted tools: {result}")
    
    # Example with visualization tool
    sine_wave_result = toolbox.safe_dispatch({
        "name": "draw_sine_wave",
        "arguments": {"frequency": 3, "duration": 7}
    })
    logger.info(f"\nResult of draw_sine_wave: {sine_wave_result}")

    
def main():
    test_case_1()


if __name__ == '__main__':
    main()