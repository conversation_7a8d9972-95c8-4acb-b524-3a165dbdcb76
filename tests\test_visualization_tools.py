from utils.logger_setup import setup_logging
from core.tool_functions import LLM_Toolbox
from tools.visualization_tools import draw_sine_wave_tool
import logging

# Set up logging
setup_logging(
    level=logging.DEBUG,
    log_file="logs/test_visualization_tools.log",  # Optional: log to file
    console=True
)
logger = logging.getLogger(__name__)

def test_case_1():
    toolbox = LLM_Toolbox()
    toolbox.register(draw_sine_wave_tool)
    
    toolbox.print_schemas()
    toolbox.print_available_tools()
    
    result = toolbox.safe_dispatch({
        "name": "draw_sine_wave",
        "arguments": {"frequency": 3, "duration": 7}
    })
    logger.info(f"Result: {result}")
    
    toolbox.safe_dispatch({
        "name": "draw_sine_wave",
        "arguments": {"frequency": 300, "duration": '7'}
    })
    logger.info(f"Result: {result}")

def main():
    test_case_1()

if __name__ == '__main__':
    main()