from transcript_parsing import Participant, Transcript, InterviewDataset

transcript_file_path = "C:\\Users\\<USER>\\AgeLab Dropbox\\<PERSON><PERSON><PERSON> Zhao\\Validated Transcripts\\Tesla\\2016k_154\\2016k_154 Stage 3 Interview audio for validation.docx"
transcript = Transcript(transcript_file_path)

transcript

print(transcript.check_metadata_found())

print(transcript.check_metadata_found(all_values=True, allow_na=False))
print(transcript.check_metadata_found(all_values=True, allow_na=True))
print(transcript.check_metadata_found(all_values=False, allow_na=False))
print(transcript.check_metadata_found(all_values=False, allow_na=True))

transcript.metadata

print(transcript.metadata.to_json())

print(transcript.metadata.to_json(simple=True))

transcript.header_para_end_idx

transcript.interview_text

transcript.interview_qa_pairs

participant_file_path = "C:\\Users\\<USER>\\AgeLab Dropbox\\<PERSON><PERSON><PERSON> Zhao\\Validated Transcripts\\Tesla\\2016k_154"
participant = Participant(participant_file_path)

participant.metadata

print(participant.metadata.to_json())

print(participant.metadata.to_json(simple=True))

participant.transcript_files

participant

participant.veh_type

