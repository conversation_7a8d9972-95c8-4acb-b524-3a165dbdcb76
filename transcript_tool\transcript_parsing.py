from docx import Document
from pathlib import Path
from dataclasses import dataclass, field, fields
from typing import Dict, List, Optional, Union, Any
import pandas as pd
import json, datetime, re

TRANSCRIPT_SUFFIXES = {'.docx'}
AUDIO_SUFFIXES = {'.mp3', '.m4a'}


@dataclass
class MetadataField:
    """Represents a single metadata field with value and optional note"""
    value: Any = None
    type: str = None
    required: bool = False
    note: Optional[str] = None
    
####################################################
@dataclass
class TranscriptMetadata:
    """Structured container for transcript metadata"""
    # From path
    participant_id: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=True))
    veh_type: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=True))
    interview_stage: MetadataField = field(default_factory=lambda: MetadataField(type='int', required=True))
    # From document
    gender: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=True))
    age: MetadataField = field(default_factory=lambda: MetadataField(type='int', required=True))
    start_date: MetadataField = field(default_factory=lambda: MetadataField(type='date', required=True))
    end_date: MetadataField = field(default_factory=lambda: MetadataField(type='date', required=True))
    
    interview_date: MetadataField = field(default_factory=lambda: MetadataField(type='date', required=False))
    training_location: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=False))
    pickup_location: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=False))
    randomization: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=False))
    key_notes: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=False))
    
    
    def to_dict(self) -> Dict[str, Dict[str, Any]]:
        """Convert metadata to dictionary format"""
        result = {}
        for f in fields(self):
            v = getattr(self, f.name)
            if v is None:
                result[f.name] = {"value": None, "type": v.type, "note": None, 'required': v.required}
            else:
                result[f.name] = {"value": v.value, "type": v.type, "note": v.note, 'required': v.required}
        return result
    
    
    def to_simple_dict(self) -> Dict[str, Any]:
        """Convert metadata to simple dictionary with just values"""
        result = {}
        for f in fields(self):
            v = getattr(self, f.name)
            if v is None:
                result[f.name] = None
            else:
                result[f.name] = v.value
        return result
    
    
    def to_json(self, simple=False) -> str:
        """Convert metadata to JSON string"""
        if simple:
            return json.dumps(self.to_simple_dict(), default=str, indent=4)
        else:
            return json.dumps(self.to_dict(), default=str, indent=4)


class Transcript:
    '''
    Parse transcript file and extract metadata and content.
    '''
    def __init__(self, file_path, header_para_threshold=30):
        self.header_para_threshold = header_para_threshold
        self.header_para_end_idx = None
        
        self.metadata = TranscriptMetadata()
        self.parsing_path(file_path) # parse metadata from file path
        self.doc_init() # parse document and extract metadata and content
    
    
    def __str__(self):
        return f'{self.metadata.participant_id.value}@Stage{self.metadata.interview_stage.value}'


    def __repr__(self):
        return f'{self.metadata.participant_id.value}@Stage{self.metadata.interview_stage.value}'
        
    
    ########### Core ###########
    def parsing_path(self, file_path):
        '''
        Parse file information from the file path, and
        parse metadata: participant_id, veh_type, and interview_stage
        from the file path.
        '''
        self.file_path = Path(file_path)
        self.file_name = self.file_path.name

        self.metadata.participant_id.value = Path(file_path).parent.name
        self.metadata.participant_id.note = 'from file path'
        
        self.metadata.veh_type.value = Path(file_path).parent.parent.name
        self.metadata.veh_type.note = 'from file path'
        
        self.metadata.interview_stage.value = self.stage_from_file_name(self.file_name)
        if self.metadata.interview_stage.value == 'N/A':
            self.metadata.interview_stage.note = 'not found in file name'
            print(f'Interview stage not found in file name: {self.file_name}')
        else:
            self.metadata.interview_stage.note = 'from file name'
            
        print(
            f'Path Parsing:\n\tPath: {self.file_path},\n\tFile name: {self.file_name},\n\tParticipant ID: {self.metadata.participant_id.value},\n\tVehicle Type: {self.metadata.veh_type.value},\n\tInterview Stage: {self.metadata.interview_stage.value}'
        )
    
    
    def doc_init(self):
        self.raw_doc = Document(self.file_path) # load docx file
        self.parsing_metadata(self.raw_doc) # parse metadata from document
        self.combine_transcripts(self.raw_doc)
        self.parse_qa_pairs()
    
    
    def parsing_metadata(self, document):
        '''
        Parse metadata from the transcript file.
        
        Args:
            document (docx.Document): The transcript file.
        '''
        for para_idx, para in enumerate(document.paragraphs):
            # Check if all required metadata is found
            if self.check_metadata_found(all_values=True, allow_na=False):
                self.header_para_end_idx = para_idx
                print(
                    f'Header para end at {para_idx} with [all] metadata found in\n' +\
                    f'{self.file_path}\n' +\
                    f'[without] NA'
                )
                break
            # Check if reached the end of the header
            if para_idx > self.header_para_threshold:
                if self.check_metadata_found(all_values=True, allow_na=True):
                    print(
                        f'Header para end at {para_idx} with [all] metadata found in\n' +\
                        f'{self.file_path}\n' +\
                        f'[with] NA'
                    )
                    self.header_para_end_idx = para_idx
                    break
                if self.check_metadata_found(all_values=False, allow_na=True):
                    print(
                        f'Header para end at {para_idx} with [required] metadata found in\n' +\
                        f'{self.file_path}\n' +\
                        f'[with] NA'
                    )
                    self.header_para_end_idx = para_idx
                    break
                if self.check_metadata_found(all_values=False, allow_na=False):
                    print(
                        f'Header para end at {para_idx} with [required] metadata found in\n' +\
                        f'{self.file_path}\n' +\
                        f'[without] NA'
                    )
                    self.header_para_end_idx = para_idx
                    break
                else:
                    print(
                        f'Required metadata not found in\n' +\
                        f'{self.file_path}'
                    )
                    break
            
            if self.metadata.interview_stage.value == 'N/A':
                m = re.search(r'\bStage\s+(\d+)\s+Interview\s+Transcription\b',
                              para.text,
                              re.IGNORECASE)
                if m:
                    self.metadata.interview_stage.value = int(m.group(1))
                    self.metadata.interview_stage.note = 'from document content'
                    
            if 'Gender:' in para.text:
                gender_value = para.text.split('Gender:')[1].strip()
                gender_value = self.clean_text(gender_value)
                if gender_value == 'F':
                    gender_value = 'Female'
                if gender_value == 'M':
                    gender_value = 'Male'
                if gender_value != 'Male' and gender_value != 'Female':
                    gender_value = 'N/A'
                self.metadata.gender.value = gender_value
                if gender_value == 'N/A':
                    self.metadata.gender.note = 'matched in document but no valid value'
                else:
                    self.metadata.gender.note = 'from document'
                
            if 'Age:' in para.text:
                s = para.text.split('Age:')[1].strip()
                if not s:
                    age_value = 'N/A'
                else:
                    age_value = int(s)
                self.metadata.age.value = age_value
                if age_value == 'N/A':
                    self.metadata.age.note = 'matched in document but no valid value'
                else:
                    self.metadata.age.note = 'from document'
                
            if 'Dates with vehicle:' in para.text:
                if '-' in para.text:
                    start_date_str = para.text.split('Dates with vehicle:')[1].strip().split('-')[0].strip()
                    self.metadata.start_date.value = self.parse_mdy(start_date_str)
                    if self.metadata.start_date.value == 'N/A':
                        self.metadata.start_date.note = 'matched in document but no valid value'
                    else:
                        self.metadata.start_date.note = 'from document'

                    end_date_str = para.text.split('Dates with vehicle:')[1].strip().split('-')[1].strip()
                    self.metadata.end_date.value = self.parse_mdy(end_date_str)
                    if self.metadata.end_date.value == 'N/A':
                        self.metadata.end_date.note = 'matched in document but no valid value'
                    else:
                        self.metadata.end_date.note = 'from document'
                elif '–' in para.text:
                    start_date_str = para.text.split('Dates with vehicle:')[1].strip().split('–')[0].strip()
                    self.metadata.start_date.value = self.parse_mdy(start_date_str)
                    if self.metadata.start_date.value == 'N/A':
                        self.metadata.start_date.note = 'matched in document but no valid value'
                    else:
                        self.metadata.start_date.note = 'from document'

                    end_date_str = para.text.split('Dates with vehicle:')[1].strip().split('–')[1].strip()
                    self.metadata.end_date.value = self.parse_mdy(end_date_str)
                    if self.metadata.end_date.value == 'N/A':
                        self.metadata.end_date.note = 'matched in document but no valid value'
                    else:
                        self.metadata.end_date.note = 'from document'
                
            if 'Date of Interview:' in para.text:
                interview_date_str = para.text.split('Date of Interview:')[1].strip()
                self.metadata.interview_date.value = self.parse_mdy(interview_date_str)
                if self.metadata.interview_date.value == 'N/A':
                    self.metadata.interview_date.note = 'matched in document but no valid value'
                else:
                    self.metadata.interview_date.note = 'from document'

            if 'Location of Training:' in para.text:
                training_location = para.text.split('Location of Training:')[1].strip()
                training_location = self.clean_text(training_location)
                if training_location == '':
                    training_location = 'N/A'
                self.metadata.training_location.value = training_location
                if training_location == 'N/A':
                    self.metadata.training_location.note = 'matched in document but no valid value'
                else:
                    self.metadata.training_location.note = 'from document'

            if 'Location of Vehicle Pickup:' in para.text:
                pickup_location = para.text.split('Location of Vehicle Pickup:')[1].strip()
                pickup_location = self.clean_text(pickup_location)
                if pickup_location == '':
                    pickup_location = 'N/A'
                self.metadata.pickup_location.value = pickup_location
                if pickup_location == 'N/A':
                    self.metadata.pickup_location.note = 'matched in document but no valid value'
                else:
                    self.metadata.pickup_location.note = 'from document'

            if 'Randomization:' in para.text:
                randomization = para.text.split('Randomization:')[1].strip()
                randomization = self.clean_text(randomization)
                if randomization == '':
                    randomization = 'N/A'
                self.metadata.randomization.value = randomization
                if randomization == 'N/A':
                    self.metadata.randomization.note = 'matched in document but no valid value'
                else:
                    self.metadata.randomization.note = 'from document'

            if 'Key Notes:' in para.text:
                key_notes = para.text.split('Key Notes:')[1].strip()
                key_notes = self.clean_text(key_notes)
                if key_notes == '':
                    key_notes = 'N/A'
                self.metadata.key_notes.value = key_notes
                if key_notes == 'N/A':
                    self.metadata.key_notes.note = 'matched in document but no valid value'
                else:
                    self.metadata.key_notes.note = 'from document'
        
        if self.header_para_end_idx is None:
            print(f'header_para_end_idx not found in {self.file_path}')
        else:
            print(f'header_para_end_idx: {self.header_para_end_idx}')
    
    ########### Utils ###########
    def stage_from_file_name(self, filename):
        p = Path(filename)
        name = p.stem.replace('_', ' ')
        m = re.search(r'Stage\s+(\d+)', name, re.IGNORECASE)
        if not m:
            return 'N/A'
        return int(m.group(1))
    
    
    def check_metadata_found(self, all_values=False, allow_na=True):
        '''
        Check if all required metadata is found.
        
        Args:
            all_values (bool): If True, check if [all] metadata values are found.
                               If False, check if [required] metadata is found.
            allow_na (bool): If True, allow 'N/A' as a valid value (matched in document but no valid value).
                             If False, 'N/A' is not considered as a valid value.
        '''
        for f in fields(self.metadata):
            v = getattr(self.metadata, f.name)
            if all_values:
                if allow_na:
                    if v is None or v.value is None:
                        return False
                else:
                    if v is None or v.value is None or v.value == 'N/A':
                        return False
                
            else:
                if allow_na:
                    if (v is None or v.value is None) and v.required:
                        return False
                else:
                    if (v is None or v.value is None or v.value == 'N/A') and v.required:
                        return False
        return True
    
    
    def clean_text(self, text):
        '''
        Clean text by removing extra spaces and newlines.
        '''
        return re.sub(r'\r\n?', '\n', text).strip()
    
    
    def parse_mdy(self, date_str):
        s = date_str.strip()
        if not s:
            return 'N/A'
        if s == 'Unknown':
            return 'N/A'
        if s == 'N/A':
            return 'N/A'
        
        s = re.split(r'\s*\(', s)[0] # remove (# days)
        for fmt in ('%m/%d/%Y', '%m/%d/%y'):
            try:
                return datetime.datetime.strptime(s, fmt).date()
            except ValueError:
                continue
            
        raise ValueError(f"Date {date_str!r} is not in a supported M/D/Y format")

        
    def combine_transcripts(self, document):
        if self.header_para_end_idx is None:
            self.interview_text = "\n".join([p.text for p in document.paragraphs if p.text.strip()]) # full transcript
            self.interview_text = self.clean_text(self.interview_text)
        else:
            self.interview_text = "\n".join([p.text for p in document.paragraphs[self.header_para_end_idx:] if p.text.strip()])
            self.interview_text = self.clean_text(self.interview_text)
    
    
    def parse_qa_pairs(self):
        participant_id = self.metadata.participant_id.value
        text = self.interview_text

        # Pattern captures speaker labels and their text
        pattern = re.compile(r'(Interviewer:|Interviewee:|{}:)\s*(.*?)(?=(Interviewer:|Interviewee:|{}:|$))'.format(participant_id, participant_id), re.DOTALL)
        matches = pattern.findall(text)

        self.interview_qa_pairs = []
        for i in range(0, len(matches)-1, 2):
            question = matches[i][1].strip()
            answer = matches[i+1][1].strip()
            self.interview_qa_pairs.append((question, answer))


####################################################
@dataclass
class ParticipantMetadata:
    """Structured container for participant metadata"""
    participant_id: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=True))
    veh_type: MetadataField = field(default_factory=lambda: MetadataField(type='str', required=True))
    
    transcript_files: MetadataField = field(default_factory=lambda: MetadataField(type='list', required=True))
    num_transcripts: MetadataField = field(default_factory=lambda: MetadataField(type='int', required=True))
    
    audio_files: MetadataField = field(default_factory=lambda: MetadataField(type='list', required=True))
    num_audio: MetadataField = field(default_factory=lambda: MetadataField(type='int', required=True))


    def to_dict(self) -> Dict[str, Dict[str, Any]]:
        """Convert metadata to dictionary format"""
        result = {}
        for f in fields(self):
            v = getattr(self, f.name)
            if v is None:
                result[f.name] = {"value": None, "type": v.type, "note": None, 'required': v.required}
            else:
                result[f.name] = {"value": v.value, "type": v.type, "note": v.note, 'required': v.required}
        return result
    
    
    def to_simple_dict(self) -> Dict[str, Any]:
        """Convert metadata to simple dictionary with just values"""
        result = {}
        for f in fields(self):
            v = getattr(self, f.name)
            if v is None:
                result[f.name] = None
            else:
                result[f.name] = v.value
        return result
    
    
    def to_json(self, simple=False) -> str:
        """Convert metadata to JSON string"""
        if simple:
            return json.dumps(self.to_simple_dict(), default=str, indent=4)
        else:
            return json.dumps(self.to_dict(), default=str, indent=4)
    

class Participant:
    def __init__(self, file_path):
        self.metadata = ParticipantMetadata()
        
        self.parsing_path(file_path)
        # self.transcripts_init() # get self.transcripts list
        
        # check and get metadata
    
    def parsing_path(self, file_path):
        self.file_path = Path(file_path)
        
        self.metadata.participant_id.value = self.file_path.name
        self.metadata.participant_id.note = 'from file path'
        
        self.metadata.veh_type.value = self.file_path.parent.name
        self.metadata.veh_type.note = 'from file path'
        
        self.metadata.transcript_files.value = [
            p for p in self.file_path.iterdir()
            if p.is_file() and p.suffix.lower() in TRANSCRIPT_SUFFIXES
        ]
        self.metadata.transcript_files.note = 'from file path'
        self.metadata.num_transcripts.value = len(self.metadata.transcript_files.value)
        self.metadata.num_transcripts.note = 'from file path'
        
        self.metadata.audio_files.value = [
            p for p in self.file_path.iterdir()
            if p.is_file() and p.suffix.lower() in AUDIO_SUFFIXES
        ]
        self.metadata.audio_files.note = 'from file path'
        self.metadata.num_audio.value = len(self.metadata.audio_files.value)
        self.metadata.num_audio.note = 'from file path'
        
        
        
    def transcripts_init(self):
        self.transcripts = []
        for transcript_path in self.transcript_files:
            self.transcripts.append(Transcript(transcript_path))
    
    def __str__(self):
        return f'{self.id}, {self.veh_type}: Transcript {self.num_transcripts}, Audio: {self.num_audio}'
    
    def __repr__(self):
        return f'{self.id}, {self.veh_type}: Transcript {self.num_transcripts}, Audio: {self.num_audio}'

    def print_parameters(self):
        print('dir \t\t\t\t(folder path):', self.dir if hasattr(self, 'dir') else 'N/A')
        print('veh_type \t\t\t(Vehicle Type):', self.veh_type if hasattr(self, 'veh_type') else 'N/A')
        print('id \t\t\t\t(paticipant ID):', self.id if hasattr(self, 'id') else 'N/A')
        print('transcript_files \t\t(transcript files):', self.transcript_files if hasattr(self, 'transcript_files') else 'N/A')
        print('num_transcripts \t\t(number of transcripts):', self.num_transcripts if hasattr(self, 'num_transcripts') else 'N/A')
        print('audio_files \t\t\t(audio files):', self.audio_files if hasattr(self, 'audio_files') else 'N/A')
        print('num_audio \t\t\t(number of audio files):', self.num_audio if hasattr(self, 'num_audio') else 'N/A')  
        print('metadata_df \t\t\t(metadata DataFrame):', self.metadata_df if hasattr(self, 'metadata_df') else 'N/A')
    
    def print_functions(self):
        print('No function defined yet.')


####################################################
class InterviewDataset:
    def __init__(self, dataset_dir: str):
        self.dir = Path(dataset_dir)
        self.metadata_df, self._by_id = self._scan_directory()
        self.metadata_df.set_index('participant_id', inplace=True)

    def _scan_directory(self) -> (pd.DataFrame | Dict[str, Participant]):
        rows = []
        by_id = {}
        for veh_type_dir in self.dir.iterdir():
            if not veh_type_dir.is_dir(): 
                continue
            veh_type = veh_type_dir.name
            for part_dir in veh_type_dir.iterdir():
                if not part_dir.is_dir() or part_dir.name == 'additional_files':
                    continue
                pid = part_dir.name
                # create Participant
                part = Participant(part_dir)  
                by_id[pid] = part
                # collect metadata row
                rows.append({
                    'participant_id': pid,
                    'veh_type':      veh_type,
                    # … you can add more metadata columns here …
                })
        df = pd.DataFrame(rows)
        return df, by_id

    def get_vehicle_types(self) -> List[str]:
        return self.metadata_df['veh_type'].unique().tolist()
    
    def get_participant_ids(self) -> List[str]:
        return self.metadata_df.index.tolist()
    
    def get_participants(
        self,
        participant_id: Optional[Union[str, List[str]]] = None,
        veh_type:      Optional[Union[str, List[str]]]     = None
    ) -> List[Participant]:
        """
        Fetch Participant objects matching the given filters.
        
        Args:
          participant_id: either a single ID or list of IDs
          veh_type: either a single vehicle type or list of types
        
        Returns:
          List of Participant instances satisfying ALL provided filters.
        """
        df = self.metadata_df

        # helper to turn single→list
        def to_list(v):
            return v if isinstance(v, (list, tuple)) else [v]

        # filter by participant_id (on the index)
        if participant_id is not None:
            ids = to_list(participant_id)
            df = df.loc[df.index.isin(ids)]

        # filter by veh_type (on the column)
        if veh_type is not None:
            types_ = to_list(veh_type)
            df = df.loc[df['veh_type'].isin(types_)]

        # map resulting participant_ids back to objects
        return [self._by_id[pid] for pid in df.index]
    
    def get_participant(self, participant_id: str) -> Participant:
        lst = self.get_participants(participant_id=participant_id)
        if not lst:
            raise KeyError(f"No participant {participant_id!r}")
        return lst[0]
    
    def get_transcripts(
        self,
        participant_id: Optional[Union[str, List[str]]] = None,
        veh_type:      Optional[Union[str, List[str]]] = None
    ) -> List[Transcript]:
        parts = self.get_participants(participant_id=participant_id,
                                      veh_type=veh_type)
        return [t for p in parts for t in p.transcripts]

####################################################
def data_ingest():
    pass

def main():
    test_meta = TranscriptMetadata()
    print(test_meta.to_simple_dict())


####################################################
if __name__ == '__main__':
    main()