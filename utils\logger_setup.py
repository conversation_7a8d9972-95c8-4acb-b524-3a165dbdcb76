import logging, sys, os
from pathlib import Path

def setup_logging(
    level=logging.INFO,
    log_file=None,
    console=True,
    log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
):
    """
    Set up logging configuration for the entire project.
    
    Args:
        level: The logging level (default: INFO)
        log_file: Optional path to a log file
        console: Whether to log to console (default: True)
        log_format: Format string for log messages
    """
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    formatter = logging.Formatter(log_format)
    
    # Console handler
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        # Set encoding to UTF-8 to handle Unicode characters
        console_handler.stream.reconfigure(encoding='utf-8', errors='backslashreplace')
        root_logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        # Ensure directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            
        # Use UTF-8 encoding for log files
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Suppress overly verbose logs from libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    return root_logger